//package com.itheima.mp.mapper;
//
//import com.itheima.mp.domain.po.User;
//import org.apache.ibatis.annotations.Param;
//
//import java.util.List;
//
//public interface UserMapper{
//
//    void saveUser(User user);
//
//    void deleteUser(Long id);
//
//    void updateUser(User user);
//
//    User queryUserById(@Param("id") Long id);
//
//    List<User> queryUserByIds(@Param("ids") List<Long> ids);
//}


package com.itheima.mp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.itheima.mp.domain.po.User;

import java.util.List;

public interface UserMapper extends BaseMapper<User> {
    User queryById(Long id);
    int saveUser(User user);
    int updateUser(User user);
    int deleteUser(User user);
    User queryUserById(Long id);
    List<User> queryUserByIds(List<Long> ids);
}