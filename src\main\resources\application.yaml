spring:
  datasource:
    url: **********************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: root
logging:
  level:
    com.itheima: debug
  pattern:
    dateformat: HH:mm:ss
mybatis-plus:
  type-aliases-package: com.itheima.mp.domain.po
  global-config:
    db-config:
      id-type: auto # 全局id类型为自增长
      mapper-locations: "classpath*:/mapper/**/*.xml" # Mapper.xml文件地址，当前这个是默认值。

knife4j:
  enable: true
  openapi:
    title: 用户管理接口文档
    description: "用户管理接口文档"
    email: <EMAIL>
    concat: 虎哥
    url: https://www.itcast.cn
    version: v1.0.0
    group:
      default:
        group-name: default
        api-rule: package
        api-rule-resources:
          - com.itheima.mp.controller
